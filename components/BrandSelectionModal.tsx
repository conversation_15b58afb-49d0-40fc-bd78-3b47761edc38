import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import { Search, X, Check, Info } from 'lucide-react-native';
import {
  professionalHairColorBrands as brands,
  Brand,
  ProductLine,
  getColorLinesByBrandId,
  getBrandsWithFormulableLines,
} from '@/constants/reference-data/brands-data';
import Colors from '@/constants/colors';
import { brandConversionService } from '@/services/brandConversionService';

interface BrandSelectionModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectBrand: (brand: string, line: string) => void;
  currentBrand?: string;
  currentLine?: string;
  title?: string;
  isConversionMode?: boolean;
  sourceBrand?: string;
  sourceLine?: string;
}

export const BrandSelectionModal: React.FC<BrandSelectionModalProps> = ({
  visible,
  onClose,
  onSelectBrand,
  currentBrand,
  currentLine,
  title = 'Seleccionar Marca y Línea',
  isConversionMode = false,
  sourceBrand,
  sourceLine,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedBrands, setExpandedBrands] = useState<Set<string>>(new Set());

  const filteredBrands = (() => {
    // Solo mostrar marcas con líneas formulables
    const brandsWithFormulableLines = getBrandsWithFormulableLines();

    if (!searchQuery.trim()) {
      return brandsWithFormulableLines;
    }

    // Filtrar por búsqueda solo entre marcas con líneas formulables
    const searchLower = searchQuery.toLowerCase();
    return brandsWithFormulableLines.filter(brand => {
      const matchesBrand = brand.name.toLowerCase().includes(searchLower);
      const matchesCountry = brand.country.toLowerCase().includes(searchLower);
      const colorLines = getColorLinesByBrandId(brand.id);
      const matchesLine = colorLines.some(line => line.name.toLowerCase().includes(searchLower));
      return matchesBrand || matchesCountry || matchesLine;
    });
  })();

  const toggleBrandExpansion = (brandName: string) => {
    const newExpanded = new Set(expandedBrands);
    if (newExpanded.has(brandName)) {
      newExpanded.delete(brandName);
    } else {
      newExpanded.add(brandName);
    }
    setExpandedBrands(newExpanded);
  };

  const handleLineSelection = (brand: Brand, line: ProductLine) => {
    onSelectBrand(brand.name, line.name);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="formSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>{title}</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <X size={24} color={Colors.light.text} />
          </TouchableOpacity>
        </View>

        <View style={styles.searchContainer}>
          <Search size={20} color={Colors.light.gray} />
          <TextInput
            style={styles.searchInput}
            placeholder="Buscar marca, país o línea..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor={Colors.light.gray}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <X size={20} color={Colors.light.gray} />
            </TouchableOpacity>
          )}
        </View>

        {isConversionMode && sourceBrand && (
          <View style={styles.conversionInfo}>
            <Info size={16} color={Colors.light.primary} />
            <Text style={styles.conversionInfoText}>
              Convirtiendo desde: {sourceBrand} {sourceLine}
            </Text>
          </View>
        )}

        <ScrollView
          style={styles.brandList}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.brandListContent}
          keyboardShouldPersistTaps="handled"
        >
          {filteredBrands.map(brand => {
            const isExpanded = expandedBrands.has(brand.name);
            const isCurrentBrand = brand.name === currentBrand;

            return (
              <View key={brand.name} style={styles.brandItem}>
                <TouchableOpacity
                  style={[styles.brandHeader, isCurrentBrand && styles.brandHeaderActive]}
                  onPress={() => toggleBrandExpansion(brand.name)}
                >
                  <View>
                    <Text style={[styles.brandName, isCurrentBrand && styles.brandNameActive]}>
                      {brand.name}
                    </Text>
                    <Text style={styles.brandCountry}>{brand.country}</Text>
                  </View>
                  <Text style={styles.expandIcon}>{isExpanded ? '−' : '+'}</Text>
                </TouchableOpacity>

                {isExpanded && (
                  <View style={styles.linesList}>
                    {getColorLinesByBrandId(brand.id).map(line => {
                      const isCurrentLine = isCurrentBrand && line.name === currentLine;
                      const hasConversion =
                        isConversionMode && sourceBrand && sourceLine
                          ? brandConversionService.hasConversion(
                              sourceBrand,
                              sourceLine,
                              brand.name,
                              line.name
                            )
                          : false;

                      return (
                        <TouchableOpacity
                          key={line.name}
                          style={[styles.lineItem, isCurrentLine && styles.lineItemActive]}
                          onPress={() => handleLineSelection(brand, line)}
                        >
                          <View style={styles.lineContent}>
                            <Text style={[styles.lineName, isCurrentLine && styles.lineNameActive]}>
                              {line.name}
                            </Text>
                            {isConversionMode && hasConversion && (
                              <View style={styles.conversionBadge}>
                                <Check size={12} color="white" />
                                <Text style={styles.conversionBadgeText}>
                                  Conversión disponible
                                </Text>
                              </View>
                            )}
                          </View>
                          {line.description && (
                            <Text style={styles.lineDescription}>{line.description}</Text>
                          )}
                        </TouchableOpacity>
                      );
                    })}
                  </View>
                )}
              </View>
            );
          })}
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.light.text,
  },
  closeButton: {
    padding: 4,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.surface,
    borderRadius: 10,
    marginHorizontal: 20,
    marginVertical: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: Colors.light.text,
  },
  brandList: {
    flex: 1,
  },
  brandListContent: {
    paddingBottom: 40,
  },
  brandItem: {
    marginBottom: 8,
  },
  brandHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: Colors.light.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
    minHeight: 60,
  },
  brandHeaderActive: {
    backgroundColor: Colors.light.primary + '10',
  },
  brandName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 4,
  },
  brandNameActive: {
    color: Colors.light.primary,
  },
  brandCountry: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  expandIcon: {
    fontSize: 24,
    color: Colors.light.gray,
  },
  linesList: {
    backgroundColor: Colors.light.surface,
  },
  lineItem: {
    paddingHorizontal: 40,
    paddingVertical: 14,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border + '30',
    minHeight: 50,
  },
  lineItemActive: {
    backgroundColor: Colors.light.primary + '15',
  },
  lineName: {
    fontSize: 15,
    color: Colors.light.text,
  },
  lineNameActive: {
    color: Colors.light.primary,
    fontWeight: '500',
  },
  lineContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  lineDescription: {
    fontSize: 13,
    color: Colors.light.gray,
    marginTop: 4,
  },
  conversionBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.success,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  conversionBadgeText: {
    fontSize: 11,
    color: Colors.light.background,
    fontWeight: '600',
  },
  conversionInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.primary + '10',
    marginHorizontal: 20,
    marginBottom: 12,
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  conversionInfoText: {
    fontSize: 14,
    color: Colors.light.primary,
    flex: 1,
  },
});
