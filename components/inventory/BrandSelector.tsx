import React, { useState, useMemo } from 'react';
import { View, Text, TouchableOpacity, TextInput, StyleSheet } from 'react-native';
import {
  Brand,
  professionalHairColorBrands,
  searchBrands,
  getBrandsWithFormulableLines,
} from '@/constants/reference-data/brands-data';
import { Ionicons } from '@expo/vector-icons';
import Colors from '@/constants/colors';

interface BrandSelectorProps {
  selectedBrand: Brand | null;
  onBrandSelect: (brand: Brand | null) => void;
  placeholder?: string;
  disabled?: boolean;
}

export function BrandSelector({
  selectedBrand,
  onBrandSelect,
  placeholder = 'Seleccionar marca',
  disabled = false,
}: BrandSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const filteredBrands = useMemo(() => {
    // Solo mostrar marcas con líneas formulables
    const brandsWithFormulableLines = getBrandsWithFormulableLines();

    if (!searchQuery.trim()) {
      return brandsWithFormulableLines;
    }

    // Filtrar por búsqueda solo entre marcas con líneas formulables
    const lowercaseQuery = searchQuery.toLowerCase();
    return brandsWithFormulableLines.filter(
      brand =>
        brand.name.toLowerCase().includes(lowercaseQuery) ||
        brand.country.toLowerCase().includes(lowercaseQuery) ||
        brand.lines.some(line =>
          line.isColorLine && line.name.toLowerCase().includes(lowercaseQuery)
        )
    );
  }, [searchQuery]);

  const handleBrandSelect = (brand: Brand) => {
    onBrandSelect(brand);
    setIsOpen(false);
    setSearchQuery('');
  };

  const handleClear = () => {
    onBrandSelect(null);
    setSearchQuery('');
  };

  const renderBrandItem = ({ item }: { item: Brand }) => {
    const formulableLines = item.lines.filter(line => line.isColorLine === true);
    return (
      <TouchableOpacity style={styles.brandItem} onPress={() => handleBrandSelect(item)}>
        <View style={styles.brandInfo}>
          <Text style={styles.brandName}>{item.name}</Text>
          <Text style={styles.brandCountry}>{item.country}</Text>
          <Text style={styles.brandLines}>
            {formulableLines.length} líneas de coloración disponibles
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  if (isOpen) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Seleccionar Marca</Text>
          <TouchableOpacity onPress={() => setIsOpen(false)}>
            <Ionicons name="close" size={24} color={Colors.light.textSecondary} />
          </TouchableOpacity>
        </View>

        <View style={styles.searchContainer}>
          <Ionicons
            name="search"
            size={20}
            color={Colors.light.textSecondary}
            style={styles.searchIcon}
          />
          <TextInput
            style={styles.searchInput}
            placeholder="Buscar marca..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            autoFocus
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color={Colors.light.textSecondary} />
            </TouchableOpacity>
          )}
        </View>

        <View style={styles.brandList}>
          {filteredBrands.map(item => (
            <View key={item.id}>{renderBrandItem({ item })}</View>
          ))}
        </View>

        {selectedBrand && (
          <TouchableOpacity style={styles.clearButton} onPress={handleClear}>
            <Text style={styles.clearButtonText}>Limpiar selección</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  }

  return (
    <TouchableOpacity
      style={[styles.selector, disabled && styles.selectorDisabled]}
      onPress={() => !disabled && setIsOpen(true)}
      disabled={disabled}
    >
      <View style={styles.selectorContent}>
        <Text style={[styles.selectorText, !selectedBrand && styles.placeholderText]}>
          {selectedBrand ? selectedBrand.name : placeholder}
        </Text>
        <Ionicons name="chevron-down" size={20} color={Colors.light.textSecondary} />
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.light.card,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.light.border,
    padding: 20,
    marginVertical: 8,
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.surface,
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 16,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
    color: Colors.light.text,
  },
  brandList: {
    maxHeight: 300,
  },
  brandItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.borderLight,
  },
  brandInfo: {
    gap: 4,
  },
  brandName: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.light.text,
  },
  brandCountry: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  brandLines: {
    fontSize: 12,
    color: Colors.light.grayLight,
  },
  clearButton: {
    marginTop: 16,
    paddingVertical: 12,
    backgroundColor: Colors.light.surface,
    borderRadius: 8,
    alignItems: 'center',
  },
  clearButtonText: {
    fontSize: 16,
    color: Colors.light.textSecondary,
  },
  selector: {
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.light.primary, // Borde dorado visible
    paddingHorizontal: 16,
    paddingVertical: 14,
    shadowColor: Colors.light.shadowColor,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  selectorDisabled: {
    backgroundColor: Colors.light.borderLight,
    opacity: 0.6,
  },
  selectorContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  selectorText: {
    fontSize: 16,
    color: Colors.light.text,
  },
  placeholderText: {
    color: Colors.light.grayLight,
  },
});
